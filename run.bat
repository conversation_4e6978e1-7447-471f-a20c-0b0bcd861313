@echo off
title Voice Cloning Studio - RunPod
color 0A

echo.
echo ========================================
echo    🎤 Voice Cloning Studio - RunPod
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo ✅ Python detected
python --version

REM Set environment variables for optimal CUDA performance
echo.
echo 🔧 Setting up environment...
set CUDA_VISIBLE_DEVICES=0
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
set PYTHONUNBUFFERED=1

REM Check CUDA availability
echo.
echo 🔍 Checking CUDA availability...
python -c "import torch; print('✅ CUDA Available:', torch.cuda.is_available()); print('🔧 Device Count:', torch.cuda.device_count()) if torch.cuda.is_available() else print('⚠️ Using CPU mode')" 2>nul
if errorlevel 1 (
    echo ⚠️ PyTorch not installed. Run setup_runpod.py first
)

REM Start the application
echo.
echo 🚀 Starting Voice Cloning Studio...
echo 📱 Local access: http://localhost:5001
echo 🌍 RunPod access: Use your pod's public URL
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

python app.py

echo.
echo 🛑 Server stopped
pause

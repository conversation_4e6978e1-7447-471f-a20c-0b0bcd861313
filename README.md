# 🎤 Voice Cloning Studio

A professional multipage Flask web application for AI-powered voice cloning. Record your voice, save it as a reference, and generate new audio clips with custom text using your cloned voice.

**✨ Optimized for Windows/CUDA and RunPod cloud GPU deployment**

## 🌟 Features

- 🎤 **Professional Voice Recording**: Browser-based recording with optimal scripts
- 📚 **Voice Library Management**: Advanced organization and playback
- 🎯 **AI Voice Cloning**: Generate speech with emotion and pacing controls
- 🎵 **High-Quality Audio**: 44.1kHz recording and generation
- 📱 **Responsive Multipage UI**: Modern interface with smooth animations
- 🔧 **Advanced Settings**: Fine-tune TTS parameters and audio quality
- 🚀 **CUDA Acceleration**: Optimized for GPU performance
- ☁️ **Cloud Ready**: Perfect for RunPod deployment

## 🚀 Quick Start

### For RunPod Users (Recommended)

1. **Create a RunPod pod** with CUDA support
2. **Upload project files** to your pod
3. **Run automated setup**:
   ```cmd
   python setup_runpod.py
   ```
4. **Start the application**:
   ```cmd
   run.bat
   ```
5. **Access via RunPod public URL**

📖 **Detailed guide**: See [RUNPOD_DEPLOYMENT.md](RUNPOD_DEPLOYMENT.md)

### For Windows Local Installation

1. **Automated Installation**:
   ```cmd
   install_windows.bat
   ```

2. **Manual Installation**:
   ```cmd
   # Install PyTorch with CUDA
   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu121

   # Install dependencies
   pip install -r requirements.txt

   # Start application
   python app.py
   ```

3. **Access the application**:
   ```
   http://localhost:5001
   ```

## Usage

### 1. Recording Your Voice
- Enter a name for your voice in the "Voice Name" field
- Click "Start Recording" and speak into your microphone
- Click "Stop Recording" when finished
- Preview your recording and click "Save Voice" to add it to your library

### 2. Managing Your Voice Library
- View all saved voices in the Voice Library section
- Click on a voice card to select it for text-to-speech generation
- Use the Play button to preview a saved voice
- Use the Delete button to remove unwanted voices

### 3. Generating Speech
- Select a voice from your library
- Enter the text you want to convert to speech
- Click "Generate Speech" to create the audio
- Play the generated audio and download it if desired

## Technical Details

- **Backend**: Flask with ChatterboxTTS for voice cloning
- **Frontend**: HTML5, CSS3 with animations, JavaScript
- **Audio**: Web Audio API for recording, torchaudio for processing
- **Storage**: Local file system for voice references and generated clips

## Requirements

- Python 3.7+
- CUDA-compatible GPU (recommended for faster processing)
- Modern web browser with microphone access
- Microphone for voice recording

## File Structure

```
voice-cloning-project/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── voice_library.json     # Voice library metadata (auto-generated)
├── templates/
│   └── index.html        # Main web interface
├── static/
│   ├── css/
│   │   └── style.css     # Styling and animations
│   ├── js/
│   │   └── main.js       # JavaScript functionality
│   ├── uploads/          # Stored voice references
│   └── generated/        # Generated audio clips
└── README.md             # This file
```

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## Troubleshooting

1. **Microphone not working**: Ensure your browser has microphone permissions
2. **Model loading errors**: Check CUDA installation and GPU availability
3. **Audio playback issues**: Verify browser audio settings and codec support

## License

This project is for educational and personal use.

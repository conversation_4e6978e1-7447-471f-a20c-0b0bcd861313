# Voice Cloning Studio

A dynamic and attractive Flask web application for voice cloning using AI. Record your voice, save it as a reference, and generate new audio clips with custom text using your cloned voice.

## Features

- 🎤 **Voice Recording**: Record audio directly in your browser
- 💾 **Voice Library**: Save, name, and manage voice references
- 🎯 **Voice Cloning**: Generate speech with custom text using saved voices
- 🎵 **Audio Playback**: Play recorded and generated audio clips
- 📱 **Responsive Design**: Modern, animated interface that works on all devices
- 🔄 **Real-time Updates**: Dynamic interface with smooth animations

## Installation

1. **Clone or navigate to the project directory**
   ```bash
   cd voice-cloning-project
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Open your browser and navigate to**
   ```
   http://localhost:5000
   ```

## Usage

### 1. Recording Your Voice
- Enter a name for your voice in the "Voice Name" field
- Click "Start Recording" and speak into your microphone
- Click "Stop Recording" when finished
- Preview your recording and click "Save Voice" to add it to your library

### 2. Managing Your Voice Library
- View all saved voices in the Voice Library section
- Click on a voice card to select it for text-to-speech generation
- Use the Play button to preview a saved voice
- Use the Delete button to remove unwanted voices

### 3. Generating Speech
- Select a voice from your library
- Enter the text you want to convert to speech
- Click "Generate Speech" to create the audio
- Play the generated audio and download it if desired

## Technical Details

- **Backend**: Flask with ChatterboxTTS for voice cloning
- **Frontend**: HTML5, CSS3 with animations, JavaScript
- **Audio**: Web Audio API for recording, torchaudio for processing
- **Storage**: Local file system for voice references and generated clips

## Requirements

- Python 3.7+
- CUDA-compatible GPU (recommended for faster processing)
- Modern web browser with microphone access
- Microphone for voice recording

## File Structure

```
voice-cloning-project/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── voice_library.json     # Voice library metadata (auto-generated)
├── templates/
│   └── index.html        # Main web interface
├── static/
│   ├── css/
│   │   └── style.css     # Styling and animations
│   ├── js/
│   │   └── main.js       # JavaScript functionality
│   ├── uploads/          # Stored voice references
│   └── generated/        # Generated audio clips
└── README.md             # This file
```

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## Troubleshooting

1. **Microphone not working**: Ensure your browser has microphone permissions
2. **Model loading errors**: Check CUDA installation and GPU availability
3. **Audio playback issues**: Verify browser audio settings and codec support

## License

This project is for educational and personal use.

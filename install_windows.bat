@echo off
title Voice Cloning Studio - Windows Installation
color 0B

echo.
echo =============================================
echo   🎤 Voice Cloning Studio - Windows Setup
echo =============================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✅ Python detected
python --version
echo.

REM Upgrade pip
echo 📦 Upgrading pip...
python -m pip install --upgrade pip
echo.

REM Install PyTorch with CUDA support
echo 🔥 Installing PyTorch with CUDA support...
echo This may take a few minutes...
python -m pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu121
if errorlevel 1 (
    echo ❌ Failed to install PyTorch with CUDA
    echo Trying CPU version...
    python -m pip install torch torchaudio
)
echo.

REM Install other dependencies
echo 📚 Installing application dependencies...
python -m pip install Flask==2.3.3 Werkzeug==2.3.7
python -m pip install numpy scipy librosa soundfile
python -m pip install psutil requests Pillow
echo.

REM Install ChatterboxTTS
echo 🎯 Installing ChatterboxTTS...
python -m pip install chatterbox-tts
if errorlevel 1 (
    echo ⚠️ ChatterboxTTS installation failed
    echo You may need to install it manually later
)
echo.

REM Create directories
echo 📁 Creating directories...
if not exist "static\uploads" mkdir "static\uploads"
if not exist "static\generated" mkdir "static\generated"
if not exist "static\css" mkdir "static\css"
if not exist "static\js" mkdir "static\js"
if not exist "templates" mkdir "templates"
echo ✅ Directories created
echo.

REM Test installation
echo 🧪 Testing installation...
python -c "import torch; print('✅ PyTorch:', torch.__version__)"
python -c "import torch; print('✅ CUDA Available:', torch.cuda.is_available())"
python -c "import torchaudio; print('✅ TorchAudio: OK')"
python -c "from chatterbox.tts import ChatterboxTTS; print('✅ ChatterboxTTS: OK')" 2>nul
if errorlevel 1 (
    echo ⚠️ ChatterboxTTS test failed - may need manual installation
)
echo.

echo 🎉 Installation completed!
echo.
echo 🚀 To start the application:
echo    1. Double-click run.bat
echo    2. Or run: python app.py
echo.
echo 📱 Access the app at: http://localhost:5001
echo 🌍 For RunPod: Use your pod's public URL
echo.
pause

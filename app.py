import os
import json
import uuid
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file, send_from_directory
from werkzeug.utils import secure_filename

# For demo purposes, we'll run without ChatterboxTTS to avoid dependency issues
# In a production environment with proper dependencies, uncomment the code below:

# try:
#     import torch
#     import torchaudio as ta
#     from chatterbox.tts import ChatterboxTTS
#
#     # Mac M1/M2/M3/M4 compatibility
#     device = "mps" if torch.backends.mps.is_available() else ("cuda" if torch.cuda.is_available() else "cpu")
#     map_location = torch.device(device)
#
#     # Patch torch.load for Mac compatibility
#     torch_load_original = torch.load
#     def patched_torch_load(*args, **kwargs):
#         if 'map_location' not in kwargs:
#             kwargs['map_location'] = map_location
#         return torch_load_original(*args, **kwargs)
#     torch.load = patched_torch_load
#
#     TTS_AVAILABLE = True
#     print(f"TTS dependencies loaded successfully. Using device: {device}")
# except ImportError as e:
#     print(f"TTS dependencies not available: {e}")
#     print("Running in demo mode - voice cloning will be simulated")
#     TTS_AVAILABLE = False
#     ta = None
#     ChatterboxTTS = None
#     device = "cpu"

# Demo mode settings
TTS_AVAILABLE = False
ta = None
ChatterboxTTS = None
device = "cpu"
print("Running in demo mode - voice cloning will be simulated")

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['GENERATED_FOLDER'] = 'static/generated'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize the TTS model
print("Loading ChatterboxTTS model...")
if TTS_AVAILABLE:
    try:
        model = ChatterboxTTS.from_pretrained(device=device)
        print(f"Model loaded successfully on {device}!")
        USE_REAL_TTS = True
    except Exception as e:
        print(f"Error loading model: {e}")
        print("Running in demo mode - TTS functionality will be simulated")
        model = None
        USE_REAL_TTS = False
else:
    print("TTS dependencies not available - running in demo mode")
    model = None
    USE_REAL_TTS = False

# Ensure upload directories exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['GENERATED_FOLDER'], exist_ok=True)

# Voice library file
VOICE_LIBRARY_FILE = 'voice_library.json'

def load_voice_library():
    """Load the voice library from JSON file"""
    if os.path.exists(VOICE_LIBRARY_FILE):
        with open(VOICE_LIBRARY_FILE, 'r') as f:
            return json.load(f)
    return {}

def save_voice_library(library):
    """Save the voice library to JSON file"""
    with open(VOICE_LIBRARY_FILE, 'w') as f:
        json.dump(library, f, indent=2)

# Multipage routes
@app.route('/')
def index():
    """Landing page"""
    return render_template('index.html', tts_available=USE_REAL_TTS, device=device)

@app.route('/record')
def record_page():
    """Voice recording page"""
    return render_template('record.html', tts_available=USE_REAL_TTS)

@app.route('/library')
def library_page():
    """Voice library page"""
    voices = load_voice_library()
    return render_template('library.html', voices=voices, tts_available=USE_REAL_TTS)

@app.route('/generate')
def generate_page():
    """Text-to-speech generation page"""
    voices = load_voice_library()
    return render_template('generate.html', voices=voices, tts_available=USE_REAL_TTS)

@app.route('/settings')
def settings_page():
    """Settings page"""
    return render_template('settings.html', tts_available=USE_REAL_TTS, device=device)

@app.route('/record', methods=['POST'])
def record_voice():
    """Handle voice recording upload"""
    if 'audio' not in request.files:
        return jsonify({'error': 'No audio file provided'}), 400

    audio_file = request.files['audio']
    voice_name = request.form.get('voice_name', '').strip()

    if not voice_name:
        return jsonify({'error': 'Voice name is required'}), 400

    if audio_file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    # Generate unique filename
    file_extension = '.wav'
    unique_filename = f"{uuid.uuid4().hex}{file_extension}"
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

    try:
        # Save the uploaded file
        audio_file.save(file_path)

        # Load and update voice library
        library = load_voice_library()
        library[voice_name] = {
            'filename': unique_filename,
            'path': file_path,
            'created_at': datetime.now().isoformat(),
            'file_size': os.path.getsize(file_path)
        }
        save_voice_library(library)

        return jsonify({
            'success': True,
            'message': f'Voice "{voice_name}" saved successfully!',
            'voice_name': voice_name
        })

    except Exception as e:
        return jsonify({'error': f'Failed to save voice: {str(e)}'}), 500

@app.route('/voices')
def get_voices():
    """Get list of saved voices"""
    voices = load_voice_library()
    return jsonify(voices)

@app.route('/voices/<voice_name>', methods=['DELETE'])
def delete_voice(voice_name):
    """Delete a saved voice"""
    library = load_voice_library()

    if voice_name not in library:
        return jsonify({'error': 'Voice not found'}), 404

    try:
        # Delete the audio file
        file_path = library[voice_name]['path']
        if os.path.exists(file_path):
            os.remove(file_path)

        # Remove from library
        del library[voice_name]
        save_voice_library(library)

        return jsonify({'success': True, 'message': f'Voice "{voice_name}" deleted successfully'})

    except Exception as e:
        return jsonify({'error': f'Failed to delete voice: {str(e)}'}), 500

@app.route('/api/generate', methods=['POST'])
def generate_speech():
    """Generate speech using selected voice and text with advanced parameters"""
    data = request.get_json()
    text = data.get('text', '').strip()
    voice_name = data.get('voice_name', '').strip()
    exaggeration = float(data.get('exaggeration', 0.5))
    cfg_weight = float(data.get('cfg_weight', 0.5))

    if not text:
        return jsonify({'error': 'Text is required'}), 400

    if not voice_name:
        return jsonify({'error': 'Voice selection is required'}), 400

    # Load voice library
    library = load_voice_library()
    if voice_name not in library:
        return jsonify({'error': 'Selected voice not found'}), 404

    try:
        output_filename = f"generated_{uuid.uuid4().hex}.wav"
        output_path = os.path.join(app.config['GENERATED_FOLDER'], output_filename)

        if USE_REAL_TTS and model and ta:
            # Get the voice reference file path
            audio_prompt_path = library[voice_name]['path']

            # Generate speech with advanced parameters
            print(f"Generating speech with voice: {voice_name}")
            print(f"Text: {text}")
            print(f"Parameters - Exaggeration: {exaggeration}, CFG Weight: {cfg_weight}")

            wav = model.generate(
                text,
                audio_prompt_path=audio_prompt_path,
                exaggeration=exaggeration,
                cfg_weight=cfg_weight
            )

            # Save generated audio
            ta.save(output_path, wav, model.sr)
        else:
            # Demo mode - copy the reference voice file as a placeholder
            import shutil
            reference_path = library[voice_name]['path']
            shutil.copy2(reference_path, output_path)
            print(f"Demo mode: Using reference voice file as placeholder for text: {text}")

        return jsonify({
            'success': True,
            'message': 'Speech generated successfully!' + (' (Demo mode - using reference audio)' if not USE_REAL_TTS else ''),
            'audio_url': f'/audio/generated/{output_filename}',
            'filename': output_filename
        })

    except Exception as e:
        print(f"Error generating speech: {e}")
        return jsonify({'error': f'Failed to generate speech: {str(e)}'}), 500

@app.route('/audio/uploads/<filename>')
def serve_upload(filename):
    """Serve uploaded audio files"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/audio/generated/<filename>')
def serve_generated(filename):
    """Serve generated audio files"""
    return send_from_directory(app.config['GENERATED_FOLDER'], filename)

if __name__ == '__main__':
    print("Starting Voice Cloning Flask App...")
    app.run(debug=True, host='0.0.0.0', port=5001)
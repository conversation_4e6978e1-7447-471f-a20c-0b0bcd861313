# 🚀 RunPod Deployment Guide - Voice Cloning Studio

This guide will help you deploy the Voice Cloning Studio on RunPod cloud GPU platform with Windows/CUDA support.

## 📋 Prerequisites

- RunPod account with GPU pod access
- Basic familiarity with RunPod interface
- Windows-based RunPod template (recommended)

## 🎯 Quick Start

### Step 1: Create RunPod Pod

1. **Login to RunPod** and go to "Pods"
2. **Select Template**: Choose a Windows-based template with CUDA support
   - Recommended: `runpod/pytorch:2.0.1-py3.10-cuda11.8-devel-ubuntu22.04`
   - Or any Windows template with Python 3.8+
3. **GPU Selection**: Choose any CUDA-compatible GPU (RTX 3090, RTX 4090, A100, etc.)
4. **Storage**: Minimum 20GB recommended
5. **Ports**: Expose port `5001` for the web interface

### Step 2: Upload Project Files

1. **Connect to your pod** via RDP or web terminal
2. **Upload all project files** to a directory (e.g., `C:\voice-cloning`)
3. **Navigate to project directory**:
   ```cmd
   cd C:\voice-cloning
   ```

### Step 3: Automated Setup

Run the automated setup script:

```cmd
python setup_runpod.py
```

This script will:
- ✅ Check system compatibility
- ✅ Install PyTorch with CUDA support
- ✅ Install all dependencies
- ✅ Install ChatterboxTTS
- ✅ Create necessary directories
- ✅ Test the installation
- ✅ Create run scripts

### Step 4: Manual Setup (Alternative)

If the automated setup fails, follow these manual steps:

#### Install PyTorch with CUDA:
```cmd
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu121
```

#### Install Dependencies:
```cmd
pip install -r requirements.txt
```

#### Install ChatterboxTTS:
```cmd
pip install chatterbox-tts
```

### Step 5: Start the Application

#### Option A: Using the run script
```cmd
run.bat
```

#### Option B: Direct command
```cmd
python app.py
```

### Step 6: Access the Application

1. **Local Access**: `http://localhost:5001`
2. **Public Access**: Use the RunPod public URL (e.g., `https://xxxxx-5001.proxy.runpod.net`)

## 🔧 Configuration

### Environment Variables

Set these for optimal performance:

```cmd
set CUDA_VISIBLE_DEVICES=0
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
```

### Port Configuration

Make sure port `5001` is exposed in your RunPod pod settings:
- Go to pod settings
- Add port mapping: `5001:5001`
- Set as HTTP port

## 🎤 Using the Application

### 1. Recording Voices
- Navigate to `/record` page
- Use the provided optimal recording scripts
- Record 30-60 seconds of clear speech
- Save with descriptive names

### 2. Voice Library
- View all saved voices at `/library`
- Play, rename, or delete voices
- Organize your voice collection

### 3. Generate Speech
- Go to `/generate` page
- Select a voice from your library
- Enter text to convert to speech
- Adjust advanced parameters:
  - **Exaggeration**: 0.5 (default) to 2.0 (dramatic)
  - **CFG Weight**: 0.1 (fast) to 1.0 (deliberate)

### 4. Settings
- Configure audio quality at `/settings`
- Adjust TTS parameters
- Monitor system status

## 🚨 Troubleshooting

### CUDA Issues
```cmd
# Check CUDA availability
python -c "import torch; print(torch.cuda.is_available())"

# Check GPU info
python -c "import torch; print(torch.cuda.get_device_name(0))"
```

### Memory Issues
- Reduce batch size in generation
- Clear CUDA cache between generations
- Monitor GPU memory usage

### ChatterboxTTS Installation Issues
```cmd
# Try alternative installation
pip install git+https://github.com/resemble-ai/chatterbox.git

# Or install from source
git clone https://github.com/resemble-ai/chatterbox.git
cd chatterbox
pip install -e .
```

### Port Access Issues
- Ensure port 5001 is exposed in RunPod settings
- Check firewall settings
- Verify the application is binding to `0.0.0.0:5001`

## 📊 Performance Optimization

### GPU Memory Management
- The app automatically clears CUDA cache
- Monitor memory usage in the console
- Restart if memory issues persist

### Audio Quality Settings
- Use 44.1kHz sample rate for best quality
- 16-bit depth is sufficient for most use cases
- WAV format recommended for generation

### Recording Tips
- Use a good microphone
- Record in a quiet environment
- Follow the provided recording scripts
- 30-60 seconds is optimal length

## 🔒 Security Considerations

- Change the Flask secret key in production
- Limit file upload sizes
- Consider authentication for public deployments
- Monitor resource usage

## 📈 Scaling

### For High Usage:
- Use larger GPU instances
- Implement request queuing
- Add load balancing
- Consider multiple pod deployment

### Storage Management:
- Regularly clean old generated files
- Implement automatic cleanup
- Monitor disk usage

## 🆘 Support

### Common Commands:
```cmd
# Check system info
python setup_runpod.py

# Test installation
python -c "from chatterbox.tts import ChatterboxTTS; print('✅ ChatterboxTTS working')"

# Monitor GPU
nvidia-smi

# Check app logs
python app.py
```

### Log Files:
- Application logs: Console output
- Error logs: Check Python traceback
- GPU logs: `nvidia-smi` command

## 🎉 Success Indicators

When everything is working correctly, you should see:

```
✅ CUDA detected! Using GPU: [GPU Name]
✅ ChatterboxTTS model loaded successfully on CUDA!
🎤 Generating speech with voice: [Voice Name]
✅ Audio saved to: [Output Path]
```

## 📞 Getting Help

If you encounter issues:

1. Check the console output for error messages
2. Verify CUDA installation with `nvidia-smi`
3. Test PyTorch CUDA with the provided commands
4. Check RunPod documentation for pod-specific issues
5. Review the application logs for detailed error information

---

**Happy Voice Cloning! 🎤✨**

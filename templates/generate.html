{% extends "base.html" %}

{% block title %}Voice Cloning Studio - Generate{% endblock %}

{% block content %}
<div class="container">
    <header class="page-header">
        <h1 class="page-title">
            <i class="fas fa-magic"></i>
            Speech Generation
        </h1>
        <p class="page-subtitle">Transform text into natural speech using your cloned voices</p>
    </header>

    <!-- Voice Selection -->
    <section class="card voice-selection-section">
        <h2 class="section-title">
            <i class="fas fa-user"></i>
            Select Voice
        </h2>
        {% if voices %}
            <div class="voice-selector">
                {% for name, data in voices.items() %}
                <div class="voice-option" data-voice="{{ name }}">
                    <div class="voice-option-content">
                        <div class="voice-avatar">
                            <i class="fas fa-microphone-alt"></i>
                        </div>
                        <div class="voice-details">
                            <h3>{{ name }}</h3>
                            <p>Created {{ data.created_at[:10] }}</p>
                            <p>{{ "%.1f"|format(data.file_size / 1024) }} KB</p>
                        </div>
                        <button class="voice-preview-btn" onclick="previewVoice('{{ name }}')">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-voices-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>No voices available. <a href="/record">Record a voice</a> first to generate speech.</p>
            </div>
        {% endif %}
    </section>

    <!-- Text Input -->
    <section class="card text-input-section">
        <h2 class="section-title">
            <i class="fas fa-keyboard"></i>
            Text to Speak
        </h2>
        <div class="text-input-container">
            <textarea id="textInput" placeholder="Enter the text you want to convert to speech..." rows="6"></textarea>
            <div class="text-stats">
                <span id="charCount">0</span> characters
                <span id="wordCount">0</span> words
                <span id="estimatedDuration">~0s</span>
            </div>
        </div>
        
        <!-- Text Presets -->
        <div class="text-presets">
            <h3>Quick Text Presets</h3>
            <div class="preset-buttons">
                <button class="preset-btn" onclick="setPresetText('greeting')">
                    <i class="fas fa-hand-wave"></i>
                    Greeting
                </button>
                <button class="preset-btn" onclick="setPresetText('announcement')">
                    <i class="fas fa-bullhorn"></i>
                    Announcement
                </button>
                <button class="preset-btn" onclick="setPresetText('story')">
                    <i class="fas fa-book"></i>
                    Story
                </button>
                <button class="preset-btn" onclick="setPresetText('technical')">
                    <i class="fas fa-cog"></i>
                    Technical
                </button>
            </div>
        </div>
    </section>

    <!-- Advanced Settings -->
    <section class="card settings-section">
        <h2 class="section-title">
            <i class="fas fa-sliders-h"></i>
            Advanced Settings
        </h2>
        <div class="settings-grid">
            <div class="setting-group">
                <label for="exaggerationSlider">
                    <i class="fas fa-theater-masks"></i>
                    Emotion Exaggeration
                </label>
                <div class="slider-container">
                    <input type="range" id="exaggerationSlider" min="0" max="2" step="0.1" value="0.5">
                    <div class="slider-labels">
                        <span>Subtle</span>
                        <span id="exaggerationValue">0.5</span>
                        <span>Dramatic</span>
                    </div>
                </div>
                <p class="setting-description">Controls emotional intensity and expressiveness</p>
            </div>

            <div class="setting-group">
                <label for="cfgWeightSlider">
                    <i class="fas fa-balance-scale"></i>
                    CFG Weight
                </label>
                <div class="slider-container">
                    <input type="range" id="cfgWeightSlider" min="0.1" max="1.0" step="0.1" value="0.5">
                    <div class="slider-labels">
                        <span>Fast</span>
                        <span id="cfgWeightValue">0.5</span>
                        <span>Deliberate</span>
                    </div>
                </div>
                <p class="setting-description">Balances speed and deliberate pacing</p>
            </div>
        </div>

        <!-- Preset Configurations -->
        <div class="preset-configs">
            <h3>Preset Configurations</h3>
            <div class="config-buttons">
                <button class="config-btn" onclick="applyPreset('default')">
                    <i class="fas fa-user"></i>
                    Default
                </button>
                <button class="config-btn" onclick="applyPreset('expressive')">
                    <i class="fas fa-theater-masks"></i>
                    Expressive
                </button>
                <button class="config-btn" onclick="applyPreset('calm')">
                    <i class="fas fa-leaf"></i>
                    Calm
                </button>
                <button class="config-btn" onclick="applyPreset('energetic')">
                    <i class="fas fa-bolt"></i>
                    Energetic
                </button>
            </div>
        </div>
    </section>

    <!-- Generation Controls -->
    <section class="card generation-section">
        <h2 class="section-title">
            <i class="fas fa-play"></i>
            Generate Speech
        </h2>
        <div class="generation-controls">
            <button id="generateBtn" class="generate-btn" disabled>
                <i class="fas fa-magic"></i>
                <span>Generate Speech</span>
            </button>
            <div class="loading-indicator" id="loadingIndicator">
                <div class="spinner"></div>
                <span>Generating speech...</span>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Generated Audio -->
    <section class="card generated-section" id="generatedSection" style="display: none;">
        <h2 class="section-title">
            <i class="fas fa-volume-up"></i>
            Generated Audio
        </h2>
        <div class="generated-content">
            <div class="audio-player">
                <audio controls id="generatedAudio"></audio>
                <div class="audio-info">
                    <span id="audioLength">Duration: --:--</span>
                    <span id="audioSize">Size: -- KB</span>
                </div>
            </div>
            <div class="audio-actions">
                <button id="playBtn" class="btn btn-primary">
                    <i class="fas fa-play"></i>
                    Play
                </button>
                <button id="downloadBtn" class="btn btn-secondary">
                    <i class="fas fa-download"></i>
                    Download
                </button>
                <button id="regenerateBtn" class="btn btn-outline">
                    <i class="fas fa-redo"></i>
                    Regenerate
                </button>
                <button id="saveToLibraryBtn" class="btn btn-outline">
                    <i class="fas fa-save"></i>
                    Save to Library
                </button>
            </div>
        </div>
    </section>

    <!-- Generation History -->
    <section class="card history-section">
        <h2 class="section-title">
            <i class="fas fa-history"></i>
            Recent Generations
        </h2>
        <div class="history-list" id="historyList">
            <div class="empty-history">
                <i class="fas fa-clock"></i>
                <p>No recent generations. Generate your first speech above!</p>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block scripts %}
<script>
// Text presets
const textPresets = {
    greeting: "Hello! Welcome to our voice cloning demonstration. I hope you're having a wonderful day and enjoying this amazing technology!",
    announcement: "Attention everyone! We're excited to announce the launch of our new AI-powered voice cloning system. This revolutionary technology will transform how we create and share audio content.",
    story: "Once upon a time, in a world where artificial intelligence and human creativity worked hand in hand, there lived a voice that could become any voice. This magical ability brought joy and wonder to people everywhere.",
    technical: "The neural network architecture utilizes advanced transformer models with attention mechanisms to generate high-quality speech synthesis. Parameters include exaggeration control and CFG weighting for optimal output."
};

// Preset configurations
const presetConfigs = {
    default: { exaggeration: 0.5, cfg_weight: 0.5 },
    expressive: { exaggeration: 0.8, cfg_weight: 0.3 },
    calm: { exaggeration: 0.2, cfg_weight: 0.7 },
    energetic: { exaggeration: 0.9, cfg_weight: 0.4 }
};

let selectedVoice = null;
let generationHistory = [];

// Voice selection
document.querySelectorAll('.voice-option').forEach(option => {
    option.addEventListener('click', function() {
        document.querySelectorAll('.voice-option').forEach(opt => opt.classList.remove('selected'));
        this.classList.add('selected');
        selectedVoice = this.dataset.voice;
        updateGenerateButton();
    });
});

// Text input handling
const textInput = document.getElementById('textInput');
textInput.addEventListener('input', function() {
    updateTextStats();
    updateGenerateButton();
});

function updateTextStats() {
    const text = textInput.value;
    const charCount = text.length;
    const wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
    const estimatedDuration = Math.ceil(wordCount / 3); // ~3 words per second
    
    document.getElementById('charCount').textContent = charCount;
    document.getElementById('wordCount').textContent = wordCount;
    document.getElementById('estimatedDuration').textContent = `~${estimatedDuration}s`;
}

function updateGenerateButton() {
    const btn = document.getElementById('generateBtn');
    const hasVoice = selectedVoice !== null;
    const hasText = textInput.value.trim().length > 0;
    
    btn.disabled = !(hasVoice && hasText);
}

// Slider handling
document.getElementById('exaggerationSlider').addEventListener('input', function() {
    document.getElementById('exaggerationValue').textContent = this.value;
});

document.getElementById('cfgWeightSlider').addEventListener('input', function() {
    document.getElementById('cfgWeightValue').textContent = this.value;
});

// Preset functions
function setPresetText(presetName) {
    textInput.value = textPresets[presetName];
    updateTextStats();
    updateGenerateButton();
}

function applyPreset(presetName) {
    const config = presetConfigs[presetName];
    document.getElementById('exaggerationSlider').value = config.exaggeration;
    document.getElementById('cfgWeightSlider').value = config.cfg_weight;
    document.getElementById('exaggerationValue').textContent = config.exaggeration;
    document.getElementById('cfgWeightValue').textContent = config.cfg_weight;
}

function previewVoice(voiceName) {
    // Play voice preview
    app.playVoice(voiceName);
}

// Generation function
document.getElementById('generateBtn').addEventListener('click', async function() {
    if (!selectedVoice || !textInput.value.trim()) return;
    
    const btn = this;
    const loadingIndicator = document.getElementById('loadingIndicator');
    
    btn.disabled = true;
    loadingIndicator.classList.add('active');
    
    try {
        const response = await fetch('/api/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                text: textInput.value.trim(),
                voice_name: selectedVoice,
                exaggeration: parseFloat(document.getElementById('exaggerationSlider').value),
                cfg_weight: parseFloat(document.getElementById('cfgWeightSlider').value)
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showGeneratedAudio(result);
            addToHistory(result);
            app.showToast(result.message, 'success');
        } else {
            app.showToast(result.error, 'error');
        }
    } catch (error) {
        app.showToast('Generation failed: ' + error.message, 'error');
    } finally {
        btn.disabled = false;
        loadingIndicator.classList.remove('active');
        updateGenerateButton();
    }
});

function showGeneratedAudio(result) {
    const section = document.getElementById('generatedSection');
    const audio = document.getElementById('generatedAudio');
    
    audio.src = result.audio_url;
    section.style.display = 'block';
    
    // Set up download button
    document.getElementById('downloadBtn').onclick = () => {
        const link = document.createElement('a');
        link.href = result.audio_url;
        link.download = result.filename;
        link.click();
    };
}

function addToHistory(result) {
    generationHistory.unshift({
        text: textInput.value.trim(),
        voice: selectedVoice,
        timestamp: new Date(),
        audioUrl: result.audio_url,
        filename: result.filename
    });
    
    // Keep only last 10 generations
    if (generationHistory.length > 10) {
        generationHistory = generationHistory.slice(0, 10);
    }
    
    updateHistoryDisplay();
}

function updateHistoryDisplay() {
    const historyList = document.getElementById('historyList');
    
    if (generationHistory.length === 0) {
        historyList.innerHTML = `
            <div class="empty-history">
                <i class="fas fa-clock"></i>
                <p>No recent generations. Generate your first speech above!</p>
            </div>
        `;
        return;
    }
    
    historyList.innerHTML = generationHistory.map(item => `
        <div class="history-item">
            <div class="history-content">
                <h4>${item.voice}</h4>
                <p>${item.text.substring(0, 100)}${item.text.length > 100 ? '...' : ''}</p>
                <small>${item.timestamp.toLocaleString()}</small>
            </div>
            <div class="history-actions">
                <button onclick="playHistoryItem('${item.audioUrl}')" class="btn btn-small">
                    <i class="fas fa-play"></i>
                </button>
                <button onclick="downloadHistoryItem('${item.audioUrl}', '${item.filename}')" class="btn btn-small">
                    <i class="fas fa-download"></i>
                </button>
            </div>
        </div>
    `).join('');
}

function playHistoryItem(audioUrl) {
    const audio = new Audio(audioUrl);
    audio.play();
}

function downloadHistoryItem(audioUrl, filename) {
    const link = document.createElement('a');
    link.href = audioUrl;
    link.download = filename;
    link.click();
}

// Initialize
updateTextStats();
updateGenerateButton();

// Check for voice parameter in URL
const urlParams = new URLSearchParams(window.location.search);
const voiceParam = urlParams.get('voice');
if (voiceParam) {
    const voiceOption = document.querySelector(`[data-voice="${voiceParam}"]`);
    if (voiceOption) {
        voiceOption.click();
    }
}
</script>
{% endblock %}

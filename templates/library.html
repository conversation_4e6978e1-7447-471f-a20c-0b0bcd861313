{% extends "base.html" %}

{% block title %}Voice Cloning Studio - Library{% endblock %}

{% block content %}
<div class="container">
    <header class="page-header">
        <h1 class="page-title">
            <i class="fas fa-folder-open"></i>
            Voice Library
        </h1>
        <p class="page-subtitle">Manage your collection of voice recordings</p>
        <div class="page-actions">
            <a href="/record" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Add New Voice
            </a>
        </div>
    </header>

    <!-- Library Stats -->
    <section class="stats-section">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-microphone"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalVoices">{{ voices|length }}</h3>
                    <p>Total Voices</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalDuration">--:--</h3>
                    <p>Total Duration</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-hdd"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalSize">-- MB</h3>
                    <p>Storage Used</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Search and Filter -->
    <section class="card search-section">
        <div class="search-controls">
            <div class="search-input-group">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Search voices by name..." class="search-input">
            </div>
            <div class="filter-controls">
                <select id="sortSelect" class="sort-select">
                    <option value="name">Sort by Name</option>
                    <option value="date">Sort by Date</option>
                    <option value="size">Sort by Size</option>
                </select>
                <button id="viewToggle" class="view-toggle" data-view="grid">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- Voice Grid -->
    <section class="voices-section">
        {% if voices %}
            <div class="voice-grid" id="voiceGrid">
                {% for name, data in voices.items() %}
                <div class="voice-card" data-voice-name="{{ name }}" data-created="{{ data.created_at }}" data-size="{{ data.file_size }}">
                    <div class="voice-card-header">
                        <h3 class="voice-name">{{ name }}</h3>
                        <div class="voice-menu">
                            <button class="menu-btn" onclick="toggleVoiceMenu('{{ name }}')">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="voice-menu-dropdown" id="menu-{{ name }}">
                                <button onclick="playVoice('{{ name }}')" class="menu-item">
                                    <i class="fas fa-play"></i> Play
                                </button>
                                <button onclick="selectVoiceForGeneration('{{ name }}')" class="menu-item">
                                    <i class="fas fa-magic"></i> Use for TTS
                                </button>
                                <button onclick="renameVoice('{{ name }}')" class="menu-item">
                                    <i class="fas fa-edit"></i> Rename
                                </button>
                                <button onclick="deleteVoice('{{ name }}')" class="menu-item danger">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="voice-waveform">
                        <div class="waveform-placeholder">
                            <i class="fas fa-music"></i>
                            <span>Audio Waveform</span>
                        </div>
                    </div>
                    
                    <div class="voice-info">
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span>{{ data.created_at[:10] }}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-weight"></i>
                            <span>{{ "%.1f"|format(data.file_size / 1024) }} KB</span>
                        </div>
                    </div>
                    
                    <div class="voice-actions">
                        <button onclick="playVoice('{{ name }}')" class="btn btn-small btn-play">
                            <i class="fas fa-play"></i>
                        </button>
                        <button onclick="selectVoiceForGeneration('{{ name }}')" class="btn btn-small btn-generate">
                            <i class="fas fa-magic"></i>
                        </button>
                        <button onclick="deleteVoice('{{ name }}')" class="btn btn-small btn-delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state" id="emptyState">
                <div class="empty-icon">
                    <i class="fas fa-microphone-slash"></i>
                </div>
                <h3>No Voices Yet</h3>
                <p>Start building your voice library by recording your first voice sample.</p>
                <a href="/record" class="btn btn-primary">
                    <i class="fas fa-microphone"></i>
                    Record Your First Voice
                </a>
            </div>
        {% endif %}
    </section>

    <!-- Bulk Actions -->
    {% if voices %}
    <section class="bulk-actions" id="bulkActions" style="display: none;">
        <div class="bulk-controls">
            <span class="selected-count">
                <span id="selectedCount">0</span> voices selected
            </span>
            <div class="bulk-buttons">
                <button id="bulkDelete" class="btn btn-danger">
                    <i class="fas fa-trash"></i>
                    Delete Selected
                </button>
                <button id="bulkExport" class="btn btn-secondary">
                    <i class="fas fa-download"></i>
                    Export Selected
                </button>
                <button id="clearSelection" class="btn btn-outline">
                    <i class="fas fa-times"></i>
                    Clear Selection
                </button>
            </div>
        </div>
    </section>
    {% endif %}
</div>

<!-- Audio Player Modal -->
<div class="modal" id="audioModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalVoiceName">Voice Preview</h3>
            <button class="modal-close" onclick="closeAudioModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <audio controls id="modalAudio" class="modal-audio"></audio>
            <div class="audio-controls">
                <button id="modalPlayBtn" class="btn btn-primary">
                    <i class="fas fa-play"></i>
                    Play
                </button>
                <button id="modalUseBtn" class="btn btn-secondary">
                    <i class="fas fa-magic"></i>
                    Use for Generation
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Voice library management functions
function toggleVoiceMenu(voiceName) {
    const menu = document.getElementById(`menu-${voiceName}`);
    // Close all other menus
    document.querySelectorAll('.voice-menu-dropdown').forEach(m => {
        if (m !== menu) m.style.display = 'none';
    });
    menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
}

function selectVoiceForGeneration(voiceName) {
    // Redirect to generation page with selected voice
    window.location.href = `/generate?voice=${encodeURIComponent(voiceName)}`;
}

function renameVoice(voiceName) {
    const newName = prompt(`Enter new name for "${voiceName}":`, voiceName);
    if (newName && newName !== voiceName) {
        // Implement rename functionality
        app.showToast('Rename functionality coming soon!', 'info');
    }
}

function closeAudioModal() {
    document.getElementById('audioModal').style.display = 'none';
    document.getElementById('modalAudio').pause();
}

// Search and filter functionality
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    document.querySelectorAll('.voice-card').forEach(card => {
        const voiceName = card.dataset.voiceName.toLowerCase();
        card.style.display = voiceName.includes(searchTerm) ? 'block' : 'none';
    });
});

// View toggle
document.getElementById('viewToggle').addEventListener('click', function() {
    const currentView = this.dataset.view;
    const newView = currentView === 'grid' ? 'list' : 'grid';
    this.dataset.view = newView;
    this.innerHTML = newView === 'grid' ? '<i class="fas fa-th"></i>' : '<i class="fas fa-list"></i>';
    document.getElementById('voiceGrid').className = `voice-${newView}`;
});

// Calculate and display stats
function updateStats() {
    const voices = {{ voices|tojson }};
    let totalSize = 0;
    Object.values(voices).forEach(voice => {
        totalSize += voice.file_size;
    });
    
    document.getElementById('totalSize').textContent = (totalSize / (1024 * 1024)).toFixed(1) + ' MB';
}

// Initialize stats
if ({{ voices|length }} > 0) {
    updateStats();
}

// Close menus when clicking outside
document.addEventListener('click', function(e) {
    if (!e.target.closest('.voice-menu')) {
        document.querySelectorAll('.voice-menu-dropdown').forEach(menu => {
            menu.style.display = 'none';
        });
    }
});
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}Voice Cloning Studio - Settings{% endblock %}

{% block content %}
<div class="container">
    <header class="page-header">
        <h1 class="page-title">
            <i class="fas fa-cog"></i>
            Settings & Configuration
        </h1>
        <p class="page-subtitle">Configure your voice cloning experience</p>
    </header>

    <!-- System Status -->
    <section class="card system-status-section">
        <h2 class="section-title">
            <i class="fas fa-desktop"></i>
            System Status
        </h2>
        <div class="status-grid">
            <div class="status-item">
                <div class="status-icon {% if tts_available %}success{% else %}warning{% endif %}">
                    <i class="fas fa-microchip"></i>
                </div>
                <div class="status-content">
                    <h3>TTS Engine</h3>
                    <p>{% if tts_available %}ChatterboxTTS Ready{% else %}Demo Mode{% endif %}</p>
                    <small>Device: {{ device|upper }}</small>
                </div>
            </div>
            
            <div class="status-item">
                <div class="status-icon success">
                    <i class="fas fa-microphone"></i>
                </div>
                <div class="status-content">
                    <h3>Audio Recording</h3>
                    <p>Web Audio API Available</p>
                    <small>Browser: Compatible</small>
                </div>
            </div>
            
            <div class="status-item">
                <div class="status-icon success">
                    <i class="fas fa-database"></i>
                </div>
                <div class="status-content">
                    <h3>Storage</h3>
                    <p>Local File System</p>
                    <small>Status: Operational</small>
                </div>
            </div>
        </div>
    </section>

    <!-- Audio Settings -->
    <section class="card audio-settings-section">
        <h2 class="section-title">
            <i class="fas fa-volume-up"></i>
            Audio Settings
        </h2>
        <div class="settings-form">
            <div class="setting-group">
                <label for="sampleRate">Sample Rate</label>
                <select id="sampleRate" class="setting-select">
                    <option value="22050">22.05 kHz (Standard)</option>
                    <option value="44100" selected>44.1 kHz (CD Quality)</option>
                    <option value="48000">48 kHz (Professional)</option>
                </select>
                <p class="setting-description">Higher sample rates provide better quality but larger file sizes</p>
            </div>

            <div class="setting-group">
                <label for="bitDepth">Bit Depth</label>
                <select id="bitDepth" class="setting-select">
                    <option value="16" selected>16-bit</option>
                    <option value="24">24-bit</option>
                    <option value="32">32-bit</option>
                </select>
                <p class="setting-description">Higher bit depth provides better dynamic range</p>
            </div>

            <div class="setting-group">
                <label for="recordingGain">Recording Gain</label>
                <div class="slider-container">
                    <input type="range" id="recordingGain" min="0.1" max="2.0" step="0.1" value="1.0">
                    <div class="slider-labels">
                        <span>Low</span>
                        <span id="gainValue">1.0x</span>
                        <span>High</span>
                    </div>
                </div>
                <p class="setting-description">Adjust microphone sensitivity</p>
            </div>
        </div>
    </section>

    <!-- TTS Parameters -->
    <section class="card tts-settings-section">
        <h2 class="section-title">
            <i class="fas fa-magic"></i>
            Default TTS Parameters
        </h2>
        <div class="settings-form">
            <div class="setting-group">
                <label for="defaultExaggeration">Default Exaggeration</label>
                <div class="slider-container">
                    <input type="range" id="defaultExaggeration" min="0" max="2" step="0.1" value="0.5">
                    <div class="slider-labels">
                        <span>Subtle</span>
                        <span id="exaggerationValue">0.5</span>
                        <span>Dramatic</span>
                    </div>
                </div>
                <p class="setting-description">Default emotion intensity for speech generation</p>
            </div>

            <div class="setting-group">
                <label for="defaultCfgWeight">Default CFG Weight</label>
                <div class="slider-container">
                    <input type="range" id="defaultCfgWeight" min="0.1" max="1.0" step="0.1" value="0.5">
                    <div class="slider-labels">
                        <span>Fast</span>
                        <span id="cfgWeightValue">0.5</span>
                        <span>Deliberate</span>
                    </div>
                </div>
                <p class="setting-description">Default pacing control for speech generation</p>
            </div>

            <div class="setting-group">
                <label for="outputFormat">Output Format</label>
                <select id="outputFormat" class="setting-select">
                    <option value="wav" selected>WAV (Uncompressed)</option>
                    <option value="mp3">MP3 (Compressed)</option>
                    <option value="flac">FLAC (Lossless)</option>
                </select>
                <p class="setting-description">Audio format for generated speech files</p>
            </div>
        </div>
    </section>

    <!-- Interface Settings -->
    <section class="card interface-settings-section">
        <h2 class="section-title">
            <i class="fas fa-palette"></i>
            Interface Settings
        </h2>
        <div class="settings-form">
            <div class="setting-group">
                <label for="theme">Theme</label>
                <select id="theme" class="setting-select">
                    <option value="auto" selected>Auto (System)</option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                </select>
                <p class="setting-description">Choose your preferred color scheme</p>
            </div>

            <div class="setting-group">
                <label for="animations">Animations</label>
                <div class="toggle-switch">
                    <input type="checkbox" id="animations" checked>
                    <label for="animations" class="toggle-label"></label>
                </div>
                <p class="setting-description">Enable or disable interface animations</p>
            </div>

            <div class="setting-group">
                <label for="autoPlay">Auto-play Generated Audio</label>
                <div class="toggle-switch">
                    <input type="checkbox" id="autoPlay" checked>
                    <label for="autoPlay" class="toggle-label"></label>
                </div>
                <p class="setting-description">Automatically play audio after generation</p>
            </div>

            <div class="setting-group">
                <label for="notifications">Desktop Notifications</label>
                <div class="toggle-switch">
                    <input type="checkbox" id="notifications">
                    <label for="notifications" class="toggle-label"></label>
                </div>
                <p class="setting-description">Show desktop notifications for completed tasks</p>
            </div>
        </div>
    </section>

    <!-- Storage Management -->
    <section class="card storage-section">
        <h2 class="section-title">
            <i class="fas fa-hdd"></i>
            Storage Management
        </h2>
        <div class="storage-info">
            <div class="storage-stats">
                <div class="stat-item">
                    <h3 id="voiceCount">0</h3>
                    <p>Voice Recordings</p>
                </div>
                <div class="stat-item">
                    <h3 id="generatedCount">0</h3>
                    <p>Generated Files</p>
                </div>
                <div class="stat-item">
                    <h3 id="totalStorage">0 MB</h3>
                    <p>Total Storage Used</p>
                </div>
            </div>
            
            <div class="storage-actions">
                <button id="cleanupBtn" class="btn btn-warning">
                    <i class="fas fa-broom"></i>
                    Clean Old Files
                </button>
                <button id="exportBtn" class="btn btn-secondary">
                    <i class="fas fa-download"></i>
                    Export All Data
                </button>
                <button id="resetBtn" class="btn btn-danger">
                    <i class="fas fa-trash"></i>
                    Reset All Data
                </button>
            </div>
        </div>
    </section>

    <!-- Advanced Settings -->
    <section class="card advanced-section">
        <h2 class="section-title">
            <i class="fas fa-tools"></i>
            Advanced Settings
        </h2>
        <div class="settings-form">
            <div class="setting-group">
                <label for="maxFileSize">Max Recording Size (MB)</label>
                <input type="number" id="maxFileSize" min="1" max="100" value="16" class="setting-input">
                <p class="setting-description">Maximum file size for voice recordings</p>
            </div>

            <div class="setting-group">
                <label for="autoSave">Auto-save Interval (seconds)</label>
                <input type="number" id="autoSave" min="0" max="300" value="30" class="setting-input">
                <p class="setting-description">0 to disable auto-save</p>
            </div>

            <div class="setting-group">
                <label for="debugMode">Debug Mode</label>
                <div class="toggle-switch">
                    <input type="checkbox" id="debugMode">
                    <label for="debugMode" class="toggle-label"></label>
                </div>
                <p class="setting-description">Enable detailed logging for troubleshooting</p>
            </div>
        </div>
    </section>

    <!-- Action Buttons -->
    <section class="settings-actions">
        <button id="saveSettings" class="btn btn-primary">
            <i class="fas fa-save"></i>
            Save Settings
        </button>
        <button id="resetSettings" class="btn btn-outline">
            <i class="fas fa-undo"></i>
            Reset to Defaults
        </button>
        <button id="exportSettings" class="btn btn-secondary">
            <i class="fas fa-download"></i>
            Export Settings
        </button>
    </section>
</div>
{% endblock %}

{% block scripts %}
<script>
// Settings management
const defaultSettings = {
    sampleRate: 44100,
    bitDepth: 16,
    recordingGain: 1.0,
    defaultExaggeration: 0.5,
    defaultCfgWeight: 0.5,
    outputFormat: 'wav',
    theme: 'auto',
    animations: true,
    autoPlay: true,
    notifications: false,
    maxFileSize: 16,
    autoSave: 30,
    debugMode: false
};

// Load settings from localStorage
function loadSettings() {
    const saved = localStorage.getItem('voiceCloningSettings');
    return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
}

// Save settings to localStorage
function saveSettings() {
    const settings = {};
    Object.keys(defaultSettings).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            if (element.type === 'checkbox') {
                settings[key] = element.checked;
            } else if (element.type === 'range' || element.type === 'number') {
                settings[key] = parseFloat(element.value);
            } else {
                settings[key] = element.value;
            }
        }
    });
    
    localStorage.setItem('voiceCloningSettings', JSON.stringify(settings));
    app.showToast('Settings saved successfully!', 'success');
}

// Apply settings to form
function applySettings(settings) {
    Object.keys(settings).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = settings[key];
            } else {
                element.value = settings[key];
            }
        }
    });
    
    // Update slider displays
    updateSliderDisplays();
}

// Update slider value displays
function updateSliderDisplays() {
    document.getElementById('gainValue').textContent = document.getElementById('recordingGain').value + 'x';
    document.getElementById('exaggerationValue').textContent = document.getElementById('defaultExaggeration').value;
    document.getElementById('cfgWeightValue').textContent = document.getElementById('defaultCfgWeight').value;
}

// Event listeners for sliders
document.getElementById('recordingGain').addEventListener('input', function() {
    document.getElementById('gainValue').textContent = this.value + 'x';
});

document.getElementById('defaultExaggeration').addEventListener('input', function() {
    document.getElementById('exaggerationValue').textContent = this.value;
});

document.getElementById('defaultCfgWeight').addEventListener('input', function() {
    document.getElementById('cfgWeightValue').textContent = this.value;
});

// Action button handlers
document.getElementById('saveSettings').addEventListener('click', saveSettings);

document.getElementById('resetSettings').addEventListener('click', function() {
    if (confirm('Reset all settings to defaults?')) {
        applySettings(defaultSettings);
        saveSettings();
    }
});

document.getElementById('exportSettings').addEventListener('click', function() {
    const settings = loadSettings();
    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'voice-cloning-settings.json';
    a.click();
    URL.revokeObjectURL(url);
});

// Storage management
document.getElementById('cleanupBtn').addEventListener('click', function() {
    if (confirm('Delete files older than 7 days?')) {
        // Implement cleanup logic
        app.showToast('Cleanup functionality coming soon!', 'info');
    }
});

document.getElementById('resetBtn').addEventListener('click', function() {
    if (confirm('This will delete ALL voice recordings and generated files. Are you sure?')) {
        // Implement reset logic
        app.showToast('Reset functionality coming soon!', 'info');
    }
});

// Theme handling
document.getElementById('theme').addEventListener('change', function() {
    const theme = this.value;
    if (theme === 'auto') {
        document.body.classList.remove('light-theme', 'dark-theme');
    } else {
        document.body.classList.remove('light-theme', 'dark-theme');
        document.body.classList.add(theme + '-theme');
    }
});

// Notifications permission
document.getElementById('notifications').addEventListener('change', function() {
    if (this.checked && 'Notification' in window) {
        Notification.requestPermission().then(permission => {
            if (permission !== 'granted') {
                this.checked = false;
                app.showToast('Notification permission denied', 'warning');
            }
        });
    }
});

// Initialize settings
document.addEventListener('DOMContentLoaded', function() {
    const settings = loadSettings();
    applySettings(settings);
    
    // Update storage stats (mock data for now)
    document.getElementById('voiceCount').textContent = '{{ voices|length }}';
    document.getElementById('generatedCount').textContent = '0';
    document.getElementById('totalStorage').textContent = '0';
});
</script>
{% endblock %}

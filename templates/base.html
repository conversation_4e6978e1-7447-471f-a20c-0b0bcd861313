<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Voice Cloning Studio{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-microphone-alt"></i>
                <span>Voice Cloning Studio</span>
            </div>
            <div class="nav-menu">
                <a href="/" class="nav-link {% if request.endpoint == 'index' %}active{% endif %}">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
                <a href="/record" class="nav-link {% if request.endpoint == 'record_page' %}active{% endif %}">
                    <i class="fas fa-microphone"></i>
                    <span>Record</span>
                </a>
                <a href="/library" class="nav-link {% if request.endpoint == 'library_page' %}active{% endif %}">
                    <i class="fas fa-folder-open"></i>
                    <span>Library</span>
                </a>
                <a href="/generate" class="nav-link {% if request.endpoint == 'generate_page' %}active{% endif %}">
                    <i class="fas fa-magic"></i>
                    <span>Generate</span>
                </a>
                <a href="/settings" class="nav-link {% if request.endpoint == 'settings_page' %}active{% endif %}">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
            <div class="nav-status">
                {% if tts_available %}
                    <span class="status-indicator online">
                        <i class="fas fa-circle"></i>
                        TTS Ready
                    </span>
                {% else %}
                    <span class="status-indicator demo">
                        <i class="fas fa-circle"></i>
                        Demo Mode
                    </span>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>

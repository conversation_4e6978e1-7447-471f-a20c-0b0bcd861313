{% extends "base.html" %}

{% block title %}Voice Cloning Studio - Home{% endblock %}

{% block content %}
<div class="container">
    <header class="hero-section">
        <div class="hero-content">
            <h1 class="hero-title">
                <i class="fas fa-microphone-alt"></i>
                Voice Cloning Studio
            </h1>
            <p class="hero-subtitle">
                Create AI-powered voice clones with state-of-the-art technology
            </p>
            <div class="hero-description">
                <p>Transform any text into natural-sounding speech using your own voice.
                   Our advanced AI model provides high-quality voice cloning with emotion control and ultra-stable output.</p>
            </div>

            {% if tts_available %}
                <div class="status-card success">
                    <i class="fas fa-check-circle"></i>
                    <div>
                        <h3>System Ready</h3>
                        <p>ChatterboxTTS loaded successfully on {{ device }}</p>
                    </div>
                </div>
            {% else %}
                <div class="status-card warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <h3>Demo Mode</h3>
                        <p>Running in demonstration mode. Install ChatterboxTTS for full functionality.</p>
                    </div>
                </div>
            {% endif %}
        </div>
    </header>

    <section class="features-section">
        <h2 class="section-title">
            <i class="fas fa-star"></i>
            Key Features
        </h2>
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-microphone"></i>
                </div>
                <h3>Voice Recording</h3>
                <p>Record high-quality voice samples directly in your browser with our optimized recording interface.</p>
                <a href="/record" class="feature-link">Start Recording <i class="fas fa-arrow-right"></i></a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-database"></i>
                </div>
                <h3>Voice Library</h3>
                <p>Manage your voice collection with easy organization, playback, and deletion capabilities.</p>
                <a href="/library" class="feature-link">View Library <i class="fas fa-arrow-right"></i></a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-magic"></i>
                </div>
                <h3>AI Generation</h3>
                <p>Generate natural speech from any text using your cloned voices with advanced emotion controls.</p>
                <a href="/generate" class="feature-link">Generate Speech <i class="fas fa-arrow-right"></i></a>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-sliders-h"></i>
                </div>
                <h3>Advanced Settings</h3>
                <p>Fine-tune generation parameters including exaggeration, CFG weight, and emotion intensity.</p>
                <a href="/settings" class="feature-link">Configure <i class="fas fa-arrow-right"></i></a>
            </div>
        </div>
    </section>

    <section class="quick-start-section">
        <h2 class="section-title">
            <i class="fas fa-rocket"></i>
            Quick Start Guide
        </h2>
        <div class="steps-container">
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h3>Record Your Voice</h3>
                    <p>Use our optimized recording script to capture the best voice sample for cloning.</p>
                </div>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h3>Save to Library</h3>
                    <p>Name and save your voice recording to build your personal voice collection.</p>
                </div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h3>Generate Speech</h3>
                    <p>Select a voice and enter any text to generate natural-sounding speech.</p>
                </div>
            </div>
        </div>

        <div class="cta-buttons">
            <a href="/record" class="btn btn-primary">
                <i class="fas fa-microphone"></i>
                Start Recording Now
            </a>
            <a href="/library" class="btn btn-secondary">
                <i class="fas fa-folder-open"></i>
                View Voice Library
            </a>
        </div>
    </section>
</div>
{% endblock %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Cloning Studio</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">
                <i class="fas fa-microphone-alt"></i>
                Voice Cloning Studio
            </h1>
            <p class="subtitle">Record your voice, clone it, and generate speech with AI</p>
        </header>

        <div class="main-content">
            <!-- Voice Recording Section -->
            <section class="card recording-section">
                <h2 class="section-title">
                    <i class="fas fa-record-vinyl"></i>
                    Record Your Voice
                </h2>
                <div class="recording-controls">
                    <input type="text" id="voiceName" placeholder="Enter voice name..." class="voice-name-input">
                    <div class="record-button-container">
                        <button id="recordBtn" class="record-btn">
                            <i class="fas fa-microphone"></i>
                            <span>Start Recording</span>
                        </button>
                        <div class="recording-indicator" id="recordingIndicator">
                            <div class="pulse"></div>
                            <span>Recording...</span>
                        </div>
                    </div>
                    <div class="audio-preview" id="audioPreview" style="display: none;">
                        <audio controls id="previewAudio"></audio>
                        <button id="saveBtn" class="save-btn">
                            <i class="fas fa-save"></i>
                            Save Voice
                        </button>
                    </div>
                </div>
            </section>

            <!-- Voice Library Section -->
            <section class="card voice-library-section">
                <h2 class="section-title">
                    <i class="fas fa-folder-open"></i>
                    Voice Library
                </h2>
                <div class="voice-grid" id="voiceGrid">
                    <!-- Voice cards will be populated here -->
                </div>
                <div class="empty-state" id="emptyState">
                    <i class="fas fa-microphone-slash"></i>
                    <p>No voices recorded yet. Start by recording your first voice!</p>
                </div>
            </section>

            <!-- Text-to-Speech Section -->
            <section class="card tts-section">
                <h2 class="section-title">
                    <i class="fas fa-volume-up"></i>
                    Generate Speech
                </h2>
                <div class="tts-controls">
                    <div class="input-group">
                        <label for="selectedVoice">Select Voice:</label>
                        <select id="selectedVoice" class="voice-select">
                            <option value="">Choose a voice...</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="textInput">Text to Speak:</label>
                        <textarea id="textInput" placeholder="Enter the text you want to convert to speech..." rows="4"></textarea>
                    </div>
                    <button id="generateBtn" class="generate-btn">
                        <i class="fas fa-magic"></i>
                        Generate Speech
                    </button>
                    <div class="loading-indicator" id="loadingIndicator">
                        <div class="spinner"></div>
                        <span>Generating speech...</span>
                    </div>
                </div>
            </section>

            <!-- Generated Audio Section -->
            <section class="card generated-section" id="generatedSection" style="display: none;">
                <h2 class="section-title">
                    <i class="fas fa-play-circle"></i>
                    Generated Audio
                </h2>
                <div class="generated-audio">
                    <audio controls id="generatedAudio"></audio>
                    <button id="downloadBtn" class="download-btn">
                        <i class="fas fa-download"></i>
                        Download
                    </button>
                </div>
            </section>
        </div>

        <!-- Toast Notifications -->
        <div class="toast-container" id="toastContainer"></div>
    </div>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>

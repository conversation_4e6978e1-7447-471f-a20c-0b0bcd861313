#!/usr/bin/env python3
"""
RunPod Setup Script for Voice Cloning Studio
Optimized for Windows/CUDA cloud GPU deployment
"""

import os
import sys
import subprocess
import platform
import torch

def print_header():
    print("=" * 60)
    print("🎤 Voice Cloning Studio - RunPod Setup")
    print("=" * 60)

def check_system():
    print("\n📋 System Information:")
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version}")
    print(f"Architecture: {platform.machine()}")
    
def check_cuda():
    print("\n🔧 CUDA Information:")
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA Available: {torch.version.cuda}")
            print(f"✅ GPU Count: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        else:
            print("❌ CUDA not available")
            return False
    except ImportError:
        print("❌ PyTorch not installed")
        return False
    return True

def install_pytorch_cuda():
    print("\n🚀 Installing PyTorch with CUDA support...")
    try:
        # Install PyTorch with CUDA 12.1 support
        cmd = [
            sys.executable, "-m", "pip", "install", 
            "torch", "torchaudio", 
            "--index-url", "https://download.pytorch.org/whl/cu121",
            "--upgrade"
        ]
        subprocess.run(cmd, check=True)
        print("✅ PyTorch with CUDA installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install PyTorch: {e}")
        return False

def install_dependencies():
    print("\n📦 Installing application dependencies...")
    try:
        # Install other requirements
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def install_chatterbox():
    print("\n🎯 Installing ChatterboxTTS...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "chatterbox-tts"], check=True)
        print("✅ ChatterboxTTS installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install ChatterboxTTS: {e}")
        print("You may need to install it manually or check for compatibility issues")
        return False

def create_directories():
    print("\n📁 Creating necessary directories...")
    directories = [
        "static/uploads",
        "static/generated",
        "static/css",
        "static/js",
        "templates"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created: {directory}")

def test_installation():
    print("\n🧪 Testing installation...")
    try:
        import torch
        import torchaudio
        from chatterbox.tts import ChatterboxTTS
        
        print("✅ All imports successful")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA ready: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️ CUDA not available, will use CPU")
            
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def create_run_script():
    print("\n📝 Creating run script...")
    
    run_script_content = '''@echo off
echo Starting Voice Cloning Studio...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Set environment variables for better performance
set CUDA_VISIBLE_DEVICES=0
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

REM Start the application
echo Starting Flask application on port 5001...
python app.py

pause
'''
    
    with open('run.bat', 'w') as f:
        f.write(run_script_content)
    
    print("✅ Created run.bat script")

def main():
    print_header()
    check_system()
    
    # Check if CUDA is available
    cuda_available = check_cuda()
    
    if not cuda_available:
        print("\n🔄 Installing PyTorch with CUDA...")
        if not install_pytorch_cuda():
            print("❌ Failed to install PyTorch with CUDA")
            return False
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        return False
    
    # Install ChatterboxTTS
    install_chatterbox()
    
    # Test installation
    if test_installation():
        print("\n✅ Installation completed successfully!")
    else:
        print("\n⚠️ Installation completed with warnings")
    
    # Create run script
    create_run_script()
    
    print("\n🎉 Setup Complete!")
    print("\nTo start the application:")
    print("1. Run: python app.py")
    print("2. Or double-click: run.bat")
    print("3. Open browser: http://localhost:5001")
    print("\n📚 For RunPod deployment:")
    print("- Expose port 5001 in your pod configuration")
    print("- Use the public URL provided by RunPod")
    
    return True

if __name__ == "__main__":
    main()

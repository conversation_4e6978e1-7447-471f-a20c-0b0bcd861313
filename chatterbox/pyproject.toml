[project]
name = "chatterbox-tts"
version = "0.1.2"
description = "Chatterbox: Open Source TTS and Voice Conversion by Resemble AI"
readme = "README.md"
requires-python = ">=3.8"
license = {file = "LICENSE"}
authors = [
    {name = "resemble-ai", email = "<EMAIL>"}
]
dependencies = [
    "numpy~=1.26.0",
    "resampy==0.4.3",
    "librosa==0.11.0",
    "s3tokenizer",
    "torch==2.6.0",
    "torchaudio==2.6.0",
    "transformers==4.46.3",
    "diffusers==0.29.0",
    "resemble-perth==1.0.1",
    "omegaconf==2.3.0",
    "conformer==0.3.2",
    "safetensors==0.5.3"
]

[project.urls]
Homepage = "https://github.com/resemble-ai/chatterbox"
Repository = "https://github.com/resemble-ai/chatterbox"

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["src"]

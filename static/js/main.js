class VoiceCloningApp {
    constructor() {
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.isRecording = false;
        this.recordedBlob = null;
        this.selectedVoice = null;
        this.recordingStartTime = null;
        this.timerInterval = null;

        this.initializeElements();
        this.bindEvents();
        this.loadVoices();
    }

    initializeElements() {
        // Recording elements (optional - only on record page)
        this.recordBtn = document.getElementById('recordBtn');
        this.recordingIndicator = document.getElementById('recordingIndicator');
        this.voiceNameInput = document.getElementById('voiceName');
        this.audioPreview = document.getElementById('audioPreview');
        this.previewAudio = document.getElementById('previewAudio');
        this.saveBtn = document.getElementById('saveBtn');
        this.recordingTimer = document.getElementById('recordingTimer');
        this.timerDisplay = document.getElementById('timerDisplay');
        this.retryBtn = document.getElementById('retryBtn');

        // Voice library elements (optional - only on library page)
        this.voiceGrid = document.getElementById('voiceGrid');
        this.emptyState = document.getElementById('emptyState');

        // TTS elements (optional - only on generate page)
        this.selectedVoiceSelect = document.getElementById('selectedVoice');
        this.textInput = document.getElementById('textInput');
        this.generateBtn = document.getElementById('generateBtn');
        this.loadingIndicator = document.getElementById('loadingIndicator');

        // Generated audio elements (optional - only on generate page)
        this.generatedSection = document.getElementById('generatedSection');
        this.generatedAudio = document.getElementById('generatedAudio');
        this.downloadBtn = document.getElementById('downloadBtn');

        // Toast container (always present)
        this.toastContainer = document.getElementById('toastContainer');
    }

    bindEvents() {
        // Recording page events
        if (this.recordBtn) {
            this.recordBtn.addEventListener('click', () => this.toggleRecording());
        }
        if (this.saveBtn) {
            this.saveBtn.addEventListener('click', () => this.saveVoice());
        }
        if (this.retryBtn) {
            this.retryBtn.addEventListener('click', () => this.retryRecording());
        }

        // Generation page events
        if (this.generateBtn) {
            this.generateBtn.addEventListener('click', () => this.generateSpeech());
        }
    }

    async toggleRecording() {
        if (!this.isRecording) {
            await this.startRecording();
        } else {
            this.stopRecording();
        }
    }

    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
            };

            this.mediaRecorder.onstop = () => {
                this.recordedBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(this.recordedBlob);
                if (this.previewAudio) {
                    this.previewAudio.src = audioUrl;
                }
                if (this.audioPreview) {
                    this.audioPreview.style.display = 'flex';
                }

                // Stop timer
                this.stopTimer();

                // Stop all tracks to release microphone
                stream.getTracks().forEach(track => track.stop());
            };

            this.mediaRecorder.start();
            this.isRecording = true;
            this.recordingStartTime = Date.now();
            this.startTimer();
            this.updateRecordingUI();
            this.showToast('Recording started...', 'success');
        } catch (error) {
            console.error('Error accessing microphone:', error);
            this.showToast('Error accessing microphone. Please check permissions.', 'error');
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;
            this.updateRecordingUI();
            this.showToast('Recording stopped', 'success');
        }
    }

    updateRecordingUI() {
        if (!this.recordBtn) return;

        if (this.isRecording) {
            this.recordBtn.innerHTML = '<i class="fas fa-stop"></i><span>Stop Recording</span>';
            this.recordBtn.classList.add('recording');
            if (this.recordingIndicator) {
                this.recordingIndicator.classList.add('active');
            }
        } else {
            this.recordBtn.innerHTML = '<i class="fas fa-microphone"></i><span>Start Recording</span>';
            this.recordBtn.classList.remove('recording');
            if (this.recordingIndicator) {
                this.recordingIndicator.classList.remove('active');
            }
        }
    }

    startTimer() {
        if (!this.timerDisplay) return;

        this.timerInterval = setInterval(() => {
            const elapsed = Date.now() - this.recordingStartTime;
            const seconds = Math.floor(elapsed / 1000);
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;

            this.timerDisplay.textContent =
                `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }

    retryRecording() {
        // Reset the recording state
        this.recordedBlob = null;
        if (this.audioPreview) {
            this.audioPreview.style.display = 'none';
        }
        if (this.timerDisplay) {
            this.timerDisplay.textContent = '00:00';
        }
        this.showToast('Ready to record again', 'info');
    }

    async saveVoice() {
        if (!this.voiceNameInput) return;

        const voiceName = this.voiceNameInput.value.trim();

        if (!voiceName) {
            this.showToast('Please enter a voice name', 'error');
            return;
        }

        if (!this.recordedBlob) {
            this.showToast('Please record audio first', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('audio', this.recordedBlob, 'recording.wav');
        formData.append('voice_name', voiceName);

        try {
            const response = await fetch('/record', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(result.message, 'success');
                this.voiceNameInput.value = '';
                if (this.audioPreview) {
                    this.audioPreview.style.display = 'none';
                }
                if (this.timerDisplay) {
                    this.timerDisplay.textContent = '00:00';
                }
                this.recordedBlob = null;
                this.loadVoices();
            } else {
                this.showToast(result.error, 'error');
            }
        } catch (error) {
            console.error('Error saving voice:', error);
            this.showToast('Error saving voice', 'error');
        }
    }

    async loadVoices() {
        try {
            const response = await fetch('/voices');
            const voices = await response.json();
            
            this.updateVoiceGrid(voices);
            this.updateVoiceSelect(voices);
        } catch (error) {
            console.error('Error loading voices:', error);
            this.showToast('Error loading voices', 'error');
        }
    }

    updateVoiceGrid(voices) {
        this.voiceGrid.innerHTML = '';
        
        if (Object.keys(voices).length === 0) {
            this.emptyState.style.display = 'block';
            return;
        }

        this.emptyState.style.display = 'none';

        Object.entries(voices).forEach(([name, data]) => {
            const voiceCard = this.createVoiceCard(name, data);
            this.voiceGrid.appendChild(voiceCard);
        });
    }

    createVoiceCard(name, data) {
        const card = document.createElement('div');
        card.className = 'voice-card';
        card.dataset.voiceName = name;

        const createdDate = new Date(data.created_at).toLocaleDateString();
        const fileSize = (data.file_size / 1024).toFixed(1);

        card.innerHTML = `
            <h3>${name}</h3>
            <div class="voice-info">
                <div>Created: ${createdDate}</div>
                <div>Size: ${fileSize} KB</div>
            </div>
            <div class="voice-actions">
                <button class="btn-small btn-play" onclick="app.playVoice('${name}')">
                    <i class="fas fa-play"></i> Play
                </button>
                <button class="btn-small btn-delete" onclick="app.deleteVoice('${name}')">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        `;

        card.addEventListener('click', (e) => {
            if (!e.target.closest('.voice-actions')) {
                this.selectVoice(name);
            }
        });

        return card;
    }

    selectVoice(voiceName) {
        // Remove previous selection
        document.querySelectorAll('.voice-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Add selection to clicked card
        const selectedCard = document.querySelector(`[data-voice-name="${voiceName}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        // Update select dropdown
        this.selectedVoiceSelect.value = voiceName;
        this.selectedVoice = voiceName;
    }

    updateVoiceSelect(voices) {
        this.selectedVoiceSelect.innerHTML = '<option value="">Choose a voice...</option>';
        
        Object.keys(voices).forEach(name => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = name;
            this.selectedVoiceSelect.appendChild(option);
        });

        this.selectedVoiceSelect.addEventListener('change', (e) => {
            if (e.target.value) {
                this.selectVoice(e.target.value);
            }
        });
    }

    async playVoice(voiceName) {
        try {
            const response = await fetch('/voices');
            const voices = await response.json();
            
            if (voices[voiceName]) {
                const audioUrl = `/audio/uploads/${voices[voiceName].filename}`;
                const audio = new Audio(audioUrl);
                audio.play();
            }
        } catch (error) {
            console.error('Error playing voice:', error);
            this.showToast('Error playing voice', 'error');
        }
    }

    async deleteVoice(voiceName) {
        if (!confirm(`Are you sure you want to delete the voice "${voiceName}"?`)) {
            return;
        }

        try {
            const response = await fetch(`/voices/${encodeURIComponent(voiceName)}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(result.message, 'success');
                this.loadVoices();
            } else {
                this.showToast(result.error, 'error');
            }
        } catch (error) {
            console.error('Error deleting voice:', error);
            this.showToast('Error deleting voice', 'error');
        }
    }

    async generateSpeech() {
        if (!this.textInput || !this.selectedVoiceSelect) return;

        const text = this.textInput.value.trim();
        const voiceName = this.selectedVoiceSelect.value;

        if (!text) {
            this.showToast('Please enter text to generate speech', 'error');
            return;
        }

        if (!voiceName) {
            this.showToast('Please select a voice', 'error');
            return;
        }

        if (this.generateBtn) {
            this.generateBtn.disabled = true;
        }
        if (this.loadingIndicator) {
            this.loadingIndicator.classList.add('active');
        }

        try {
            // Get advanced parameters if available
            const exaggeration = document.getElementById('exaggerationSlider')?.value || 0.5;
            const cfgWeight = document.getElementById('cfgWeightSlider')?.value || 0.5;

            const response = await fetch('/api/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: text,
                    voice_name: voiceName,
                    exaggeration: parseFloat(exaggeration),
                    cfg_weight: parseFloat(cfgWeight)
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(result.message, 'success');
                if (this.generatedAudio) {
                    this.generatedAudio.src = result.audio_url;
                }
                if (this.generatedSection) {
                    this.generatedSection.style.display = 'block';
                }

                // Set up download button
                if (this.downloadBtn) {
                    this.downloadBtn.onclick = () => {
                        const link = document.createElement('a');
                        link.href = result.audio_url;
                        link.download = result.filename;
                        link.click();
                    };
                }
            } else {
                this.showToast(result.error, 'error');
            }
        } catch (error) {
            console.error('Error generating speech:', error);
            this.showToast('Error generating speech', 'error');
        } finally {
            if (this.generateBtn) {
                this.generateBtn.disabled = false;
            }
            if (this.loadingIndicator) {
                this.loadingIndicator.classList.remove('active');
            }
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        this.toastContainer.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            toast.remove();
        }, 5000);

        // Remove on click
        toast.addEventListener('click', () => {
            toast.remove();
        });
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new VoiceCloningApp();
});

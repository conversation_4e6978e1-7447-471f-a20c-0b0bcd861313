class VoiceCloningApp {
    constructor() {
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.isRecording = false;
        this.recordedBlob = null;
        this.selectedVoice = null;
        
        this.initializeElements();
        this.bindEvents();
        this.loadVoices();
    }

    initializeElements() {
        // Recording elements
        this.recordBtn = document.getElementById('recordBtn');
        this.recordingIndicator = document.getElementById('recordingIndicator');
        this.voiceNameInput = document.getElementById('voiceName');
        this.audioPreview = document.getElementById('audioPreview');
        this.previewAudio = document.getElementById('previewAudio');
        this.saveBtn = document.getElementById('saveBtn');

        // Voice library elements
        this.voiceGrid = document.getElementById('voiceGrid');
        this.emptyState = document.getElementById('emptyState');

        // TTS elements
        this.selectedVoiceSelect = document.getElementById('selectedVoice');
        this.textInput = document.getElementById('textInput');
        this.generateBtn = document.getElementById('generateBtn');
        this.loadingIndicator = document.getElementById('loadingIndicator');

        // Generated audio elements
        this.generatedSection = document.getElementById('generatedSection');
        this.generatedAudio = document.getElementById('generatedAudio');
        this.downloadBtn = document.getElementById('downloadBtn');

        // Toast container
        this.toastContainer = document.getElementById('toastContainer');
    }

    bindEvents() {
        this.recordBtn.addEventListener('click', () => this.toggleRecording());
        this.saveBtn.addEventListener('click', () => this.saveVoice());
        this.generateBtn.addEventListener('click', () => this.generateSpeech());
    }

    async toggleRecording() {
        if (!this.isRecording) {
            await this.startRecording();
        } else {
            this.stopRecording();
        }
    }

    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
            };

            this.mediaRecorder.onstop = () => {
                this.recordedBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(this.recordedBlob);
                this.previewAudio.src = audioUrl;
                this.audioPreview.style.display = 'flex';
                
                // Stop all tracks to release microphone
                stream.getTracks().forEach(track => track.stop());
            };

            this.mediaRecorder.start();
            this.isRecording = true;
            this.updateRecordingUI();
            this.showToast('Recording started...', 'success');
        } catch (error) {
            console.error('Error accessing microphone:', error);
            this.showToast('Error accessing microphone. Please check permissions.', 'error');
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;
            this.updateRecordingUI();
            this.showToast('Recording stopped', 'success');
        }
    }

    updateRecordingUI() {
        if (this.isRecording) {
            this.recordBtn.innerHTML = '<i class="fas fa-stop"></i><span>Stop Recording</span>';
            this.recordBtn.classList.add('recording');
            this.recordingIndicator.classList.add('active');
        } else {
            this.recordBtn.innerHTML = '<i class="fas fa-microphone"></i><span>Start Recording</span>';
            this.recordBtn.classList.remove('recording');
            this.recordingIndicator.classList.remove('active');
        }
    }

    async saveVoice() {
        const voiceName = this.voiceNameInput.value.trim();
        
        if (!voiceName) {
            this.showToast('Please enter a voice name', 'error');
            return;
        }

        if (!this.recordedBlob) {
            this.showToast('Please record audio first', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('audio', this.recordedBlob, 'recording.wav');
        formData.append('voice_name', voiceName);

        try {
            const response = await fetch('/record', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(result.message, 'success');
                this.voiceNameInput.value = '';
                this.audioPreview.style.display = 'none';
                this.recordedBlob = null;
                this.loadVoices();
            } else {
                this.showToast(result.error, 'error');
            }
        } catch (error) {
            console.error('Error saving voice:', error);
            this.showToast('Error saving voice', 'error');
        }
    }

    async loadVoices() {
        try {
            const response = await fetch('/voices');
            const voices = await response.json();
            
            this.updateVoiceGrid(voices);
            this.updateVoiceSelect(voices);
        } catch (error) {
            console.error('Error loading voices:', error);
            this.showToast('Error loading voices', 'error');
        }
    }

    updateVoiceGrid(voices) {
        this.voiceGrid.innerHTML = '';
        
        if (Object.keys(voices).length === 0) {
            this.emptyState.style.display = 'block';
            return;
        }

        this.emptyState.style.display = 'none';

        Object.entries(voices).forEach(([name, data]) => {
            const voiceCard = this.createVoiceCard(name, data);
            this.voiceGrid.appendChild(voiceCard);
        });
    }

    createVoiceCard(name, data) {
        const card = document.createElement('div');
        card.className = 'voice-card';
        card.dataset.voiceName = name;

        const createdDate = new Date(data.created_at).toLocaleDateString();
        const fileSize = (data.file_size / 1024).toFixed(1);

        card.innerHTML = `
            <h3>${name}</h3>
            <div class="voice-info">
                <div>Created: ${createdDate}</div>
                <div>Size: ${fileSize} KB</div>
            </div>
            <div class="voice-actions">
                <button class="btn-small btn-play" onclick="app.playVoice('${name}')">
                    <i class="fas fa-play"></i> Play
                </button>
                <button class="btn-small btn-delete" onclick="app.deleteVoice('${name}')">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        `;

        card.addEventListener('click', (e) => {
            if (!e.target.closest('.voice-actions')) {
                this.selectVoice(name);
            }
        });

        return card;
    }

    selectVoice(voiceName) {
        // Remove previous selection
        document.querySelectorAll('.voice-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Add selection to clicked card
        const selectedCard = document.querySelector(`[data-voice-name="${voiceName}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        // Update select dropdown
        this.selectedVoiceSelect.value = voiceName;
        this.selectedVoice = voiceName;
    }

    updateVoiceSelect(voices) {
        this.selectedVoiceSelect.innerHTML = '<option value="">Choose a voice...</option>';
        
        Object.keys(voices).forEach(name => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = name;
            this.selectedVoiceSelect.appendChild(option);
        });

        this.selectedVoiceSelect.addEventListener('change', (e) => {
            if (e.target.value) {
                this.selectVoice(e.target.value);
            }
        });
    }

    async playVoice(voiceName) {
        try {
            const response = await fetch('/voices');
            const voices = await response.json();
            
            if (voices[voiceName]) {
                const audioUrl = `/audio/uploads/${voices[voiceName].filename}`;
                const audio = new Audio(audioUrl);
                audio.play();
            }
        } catch (error) {
            console.error('Error playing voice:', error);
            this.showToast('Error playing voice', 'error');
        }
    }

    async deleteVoice(voiceName) {
        if (!confirm(`Are you sure you want to delete the voice "${voiceName}"?`)) {
            return;
        }

        try {
            const response = await fetch(`/voices/${encodeURIComponent(voiceName)}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(result.message, 'success');
                this.loadVoices();
            } else {
                this.showToast(result.error, 'error');
            }
        } catch (error) {
            console.error('Error deleting voice:', error);
            this.showToast('Error deleting voice', 'error');
        }
    }

    async generateSpeech() {
        const text = this.textInput.value.trim();
        const voiceName = this.selectedVoiceSelect.value;

        if (!text) {
            this.showToast('Please enter text to generate speech', 'error');
            return;
        }

        if (!voiceName) {
            this.showToast('Please select a voice', 'error');
            return;
        }

        this.generateBtn.disabled = true;
        this.loadingIndicator.classList.add('active');

        try {
            const response = await fetch('/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: text,
                    voice_name: voiceName
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(result.message, 'success');
                this.generatedAudio.src = result.audio_url;
                this.generatedSection.style.display = 'block';
                
                // Set up download button
                this.downloadBtn.onclick = () => {
                    const link = document.createElement('a');
                    link.href = result.audio_url;
                    link.download = result.filename;
                    link.click();
                };
            } else {
                this.showToast(result.error, 'error');
            }
        } catch (error) {
            console.error('Error generating speech:', error);
            this.showToast('Error generating speech', 'error');
        } finally {
            this.generateBtn.disabled = false;
            this.loadingIndicator.classList.remove('active');
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        this.toastContainer.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            toast.remove();
        }, 5000);

        // Remove on click
        toast.addEventListener('click', () => {
            toast.remove();
        });
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new VoiceCloningApp();
});

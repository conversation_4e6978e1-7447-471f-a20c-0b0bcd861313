/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    animation: fadeInDown 1s ease-out;
}

.title {
    font-size: 3rem;
    color: white;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.title i {
    margin-right: 15px;
    animation: pulse 2s infinite;
}

.subtitle {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
    font-weight: 300;
}

/* Card Styles */
.card {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    animation: fadeInUp 0.8s ease-out;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

.section-title {
    font-size: 1.8rem;
    margin-bottom: 25px;
    color: #4a5568;
    display: flex;
    align-items: center;
    gap: 15px;
}

.section-title i {
    color: #667eea;
}

/* Recording Section */
.recording-controls {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.voice-name-input {
    width: 100%;
    max-width: 400px;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.voice-name-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.record-button-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.record-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 20px 40px;
    border-radius: 50px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.record-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
}

.record-btn.recording {
    background: linear-gradient(135deg, #ff4757, #c44569);
    animation: recordingPulse 1.5s infinite;
}

.recording-indicator {
    display: none;
    align-items: center;
    gap: 15px;
    color: #ff4757;
    font-weight: 600;
}

.recording-indicator.active {
    display: flex;
}

.pulse {
    width: 20px;
    height: 20px;
    background: #ff4757;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

.audio-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 15px;
    width: 100%;
    max-width: 500px;
}

.save-btn {
    background: linear-gradient(135deg, #2ed573, #1e90ff);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(46, 213, 115, 0.4);
}

/* Voice Library */
.voice-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.voice-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.voice-card:hover {
    transform: translateY(-3px);
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.voice-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.voice-card h3 {
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.voice-card .voice-info {
    font-size: 0.9rem;
    opacity: 0.7;
    margin-bottom: 15px;
}

.voice-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.btn-small {
    padding: 8px 15px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-play {
    background: #667eea;
    color: white;
}

.btn-delete {
    background: #ff6b6b;
    color: white;
}

.btn-small:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* TTS Section */
.tts-controls {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.input-group label {
    font-weight: 600;
    color: #4a5568;
}

.voice-select, textarea {
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.voice-select:focus, textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

textarea {
    resize: vertical;
    min-height: 120px;
}

.generate-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 18px 40px;
    border-radius: 50px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    align-self: center;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.generate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.loading-indicator {
    display: none;
    align-items: center;
    justify-content: center;
    gap: 15px;
    color: #667eea;
    font-weight: 600;
}

.loading-indicator.active {
    display: flex;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Generated Audio Section */
.generated-audio {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.download-btn {
    background: linear-gradient(135deg, #2ed573, #17a2b8);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(46, 213, 115, 0.4);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.toast {
    background: white;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    border-left: 4px solid #667eea;
    animation: slideInRight 0.3s ease-out;
    max-width: 350px;
}

.toast.success {
    border-left-color: #2ed573;
}

.toast.error {
    border-left-color: #ff6b6b;
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
}

@keyframes recordingPulse {
    0%, 100% {
        box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
    }
    50% {
        box-shadow: 0 5px 25px rgba(255, 107, 107, 0.8);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .card {
        padding: 20px;
    }
    
    .voice-grid {
        grid-template-columns: 1fr;
    }
    
    .recording-controls {
        align-items: stretch;
    }
    
    .record-btn {
        padding: 15px 30px;
    }
}
